import requests

headers = {
    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0',
    'Accept': 'application/json',
    'Accept-Language': 'en-US,en;q=0.5',
    # 'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Authorization': 'Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'X-Requested-With': 'XMLHttpRequest',
    'Origin': 'https://sttn.sociair.com',
    'Connection': 'keep-alive',
    'Referer': 'https://sttn.sociair.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
}

response = requests.get('https://central-api.sociair.com/api/v1/hr/member-dashboard/card', headers=headers)
print(response.json())